'use client'

import Image from 'next/image'
import { useState } from 'react'

interface FAQItem {
  question: string
  answer: string
}

interface FAQProps {
  items?: FAQItem[]
  className?: string
}

const defaultFAQItems: FAQItem[] = [
  {
    question: "How can I track my shipment?",
    answer: "KoolLogistics offers a range of solar-powered refrigeration designed to meet diverse needs. Whether for homes, businesses, our products ensure reliable cooling without reliance on traditional energy sources."
  },
  {
    question: "What services do you offer?",
    answer: "We offer comprehensive logistics solutions including freight forwarding, warehousing, distribution, and supply chain management services."
  },
  {
    question: "How long does shipping take?",
    answer: "Shipping times vary depending on the destination and service level selected. Standard shipping typically takes 3-7 business days."
  },
  {
    question: "Do you offer door-to-door delivery?",
    answer: "Yes, we offer door-to-door delivery services for your convenience. Our team will pick up and deliver your packages directly."
  }
]

export default function FAQ({ items = defaultFAQItems, className = "" }: FAQProps) {
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(0)

  return (
    <div className={`py-16 px-6 ${className}`}>
      <div className="max-w-[800px] mx-auto">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-6">
            <Image
              src="/icons/cubeicon.svg"
              alt="Cube Icon"
              width={16}
              height={16}
           
            />
            <p className="text-small font-semibold text-[#454545] ">FAQs</p>
          </div>
          <h2 className="h3-semibold sm:h2-semibold sm:text-[36px] font-semibold text-[40px] leading-[120%] text-center text-[#171717]">
            Get all of your<br />
            questions answered
          </h2>
        </div>

        <div className="space-y-4">
          {items.map((item, index) => (
            <div key={index} className="border border-[#E5E5E5] rounded-[12px] overflow-hidden">
              <button
                onClick={() => setExpandedFAQ(expandedFAQ === index ? null : index)}
                className="w-full px-6 py-5 text-left flex items-center justify-between bg-white transition-colors"
              >
                <span className="text-[18px] font-semibold text-[#171717]">{item.question}</span>
                <div className="w-6 h-6 flex items-center justify-center">
                  <Image
                    src="/icons/ArrowUpRight.svg"
                    alt="Arrow Up Right"
                    width={24}
                    height={24}
                    className="w-4 h-4"
                  />
                </div>
              </button>
              {expandedFAQ === index && (
                <div className="px-6 pb-5 bg-white">
                  <p className=" text-[16px] leading-[1.6]">
                    {item.answer}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
