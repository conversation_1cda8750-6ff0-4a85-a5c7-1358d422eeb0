'use client'

import React, { useState } from 'react'
import SendButton from '@/components/ui/SendButton'

const ContactForm = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    message: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
  }

  return (
    <div className="order-2 lg:order-2">
      <h2 className="text-[24px] md:text-[28px] lg:text-[32px] font-semibold text-woodsmoke-950 mb-3 md:mb-4">Send a Message</h2>
      <p className="text-woodsmoke-800 text-medium text-[18px] md:text-[16px] leading-[1.6] mb-6 md:mb-8">
        We&apos;d love to hear from you. Our friendly team is always here to chat.
      </p>

      <div className="lg:h-[600px] lg:overflow-y-auto lg:scrollbar-hide lg:pr-2" style={{scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
        <form onSubmit={handleSubmit} className="space-y-4 md:space-y-6">
          {/* Name Fields */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
            <div>
              <label className="block text-woodsmoke-700: #4F4F4F text-[14px] md:text-[14px] font-medium mb-1 md:mb-2">
                First name
              </label>
              <input
                type="text"
                name="firstName"
                placeholder="First name"
                value={formData.firstName}
                onChange={handleInputChange}
                className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[14px] md:text-[16px] placeholder-[#6D6D6D] focus:outline-none focus:outline-ring/50 transition-colors"
              />
            </div>
            <div>
              <label className="block text-woodsmoke-700: #4F4F4F text-[14px] md:text-[14px] font-medium mb-1 md:mb-2">
                Last name
              </label>
              <input
                type="text"
                name="lastName"
                placeholder="Last name"
                value={formData.lastName}
                onChange={handleInputChange}
                className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[14px] md:text-[16px] placeholder-[#6D6D6D] focus:outline-none focus:outline-ring/50 transition-colors"
              />
            </div>
          </div>

          {/* Email Field */}
          <div>
            <label className="block text-woodsmoke-700: #4F4F4F text-[14px] md:text-[14px] font-medium mb-1 md:mb-2">
              Email
            </label>
            <input
              type="email"
              name="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[14px] md:text-[16px] placeholder-[#6D6D6D] focus:outline-none focus:outline-ring/50 transition-colors"
            />
          </div>

          {/* Phone Field */}
          <div>
            <label className="block text-woodsmoke-700: #4F4F4F text-[14px] md:text-[14px] font-medium mb-1 md:mb-2">
              Phone number
            </label>
            <div className="flex">
              <select className="px-2 md:px-3 py-2 md:py-3 border border-[#E5E5E5] border-r-0 rounded-l-[6px] md:rounded-l-[8px] bg-white text-[14px] md:text-[16px] focus:outline-none focus:outline-ring/50">
                <option>NG</option>
              </select>
              <input
                type="tel"
                name="phoneNumber"
                placeholder="+234 803 123 456"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                className="flex-1 px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-r-[6px] md:rounded-r-[8px] text-[14px] md:text-[16px] placeholder-[#6D6D6D] focus:outline-none focus:outline-ring/50 transition-colors"
              />
            </div>
          </div>

          {/* Message Field */}
          <div>
            <label className="block text-woodsmoke-700: #4F4F4F text-[14px] md:text-[14px] font-medium mb-1 md:mb-2">
              Message
            </label>
            <textarea
              name="message"
              rows={4}
              placeholder="Leave us a message..."
              value={formData.message}
              onChange={handleInputChange}
              className="w-full px-3 md:px-4 py-2 md:py-3 border border-[#E5E5E5] rounded-[6px] md:rounded-[8px] text-[14px] md:text-[16px] placeholder-[#6D6D6D] focus:outline-none focus:outline-ring/50 transition-colors resize-none"
            ></textarea>
          </div>

          {/* Send Message Button */}
          <div className="pt-3 md:pt-4">
            <SendButton type="submit">
              Send Message
            </SendButton>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ContactForm