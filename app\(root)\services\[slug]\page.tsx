'use client'
import React from 'react'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import FAQ from '@/components/FAQ'
import { getServiceBySlug } from '@/data/servicesData'

interface ServiceDetailPageProps {
  params: Promise<{
    slug: string
  }>
}

const ServiceDetailPage = ({ params }: ServiceDetailPageProps) => {
  const { slug } = React.use(params)
  const serviceData = getServiceBySlug(slug)
  
  if (!serviceData) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-white pt-14">
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      
      {/* Back Button */}
      <div className="pt-6 md:pt-8 pb-6 md:pb-8">
        <div className="max-w-7xl mx-auto px-6">
          <Link
            href="/services"
            className="flex items-center gap-2 text-[#F57D1C]"
          >
            <Image
              src="/icons/Backarrow.svg"
              alt="Back Arrow"
              width={32}
              height={32}
              className="w-4 h-4 md:w-5 md:h-5"
            />
            <span className="text-[16px] text-regular md:text-[16px] text-[#171717] font-medium">Back to Services</span>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="pb-12 md:pb-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-6 md:gap-8 lg:gap-12 items-start">
            {/* Image - Shows first on mobile, left on desktop */}
            <div className="relative order-1 lg:order-1">
              <Image
                src={serviceData.image.src}
                alt={serviceData.image.alt}
                width={576}
                height={488}
               
              />
            </div>

            {/* Content - Shows second on mobile, right on desktop */}
            <div className="order-2 lg:order-2 lg:h-[488px] lg:overflow-y-auto lg:scrollbar-hide space-y-6 md:space-y-8" style={{scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
              <div>
                <h1 className="lg:text-[40px] h3-semibold sm:h3-semibold text-[32px] text-[#171717]  mb-4 md:mb-6">
                  {serviceData.title}
                </h1>

                <div className="space-y-3 md:space-y-4 text-[#666666]">
                  {serviceData.content.introduction.map((paragraph, index) => (
                    <p key={index} className="text-regular text-woodsmoke-800 text-[16px] md:text-[16px] leading-[150%]">
                      {paragraph}
                    </p>
                  ))}
                </div>

                <ul className="mt-4 md:mt-6 space-y-2 md:space-y-3">
                  {serviceData.content.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2 md:gap-3 text-regular text-[#454545] text-[16px] md:text-[16px]">
                      <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Dynamic Sections */}
              {serviceData.content.sections.map((section, index) => (
                <div key={index} className="pt-1 md:pt-2">
                  <h2 className="text-large font-semibold text-[20px] leading-[140%] tracking-[0%] text-[#171717] mb-3 md:mb-4">
                    {section.title}
                  </h2>
                  <div className="text-[#454545]">
                    <p className="text-regular font-normal text-[16px] leading-[150%] tracking-[0%]">
                      {section.content}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <FAQ />
    </div>
  )
}

export default ServiceDetailPage
