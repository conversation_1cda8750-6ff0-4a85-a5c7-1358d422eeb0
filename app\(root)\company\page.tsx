'use client'
import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import LandingPageContact from '@/components/cards/LandingPageContact'

const Company = () => {
  const [activeTab, setActiveTab] = useState('mission')
  return (
    <div className="min-h-screen pt-14">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-[40px]">
        <div className="grid lg:grid-cols-2 gap-6 md:gap-12 items-start mb-8 md:mb-12">
          <div className="space-y-4 md:space-y-6">
            <div className="flex items-center gap-2">
              <Image
                src="/icons/cubeicon.svg"
                alt="Cube Icon"
                width={20}
                height={20}
                className="w-4 h-4 md:w-5 md:h-5"
              />
              <p className="text-woodsmoke-800: #454545 font-medium text-small md:text-small sm:text-small sm:font-semibold sm:text-[14px] tracking-wider uppercase">WHO WE ARE</p>
            </div>
            <h1 className="text-woodsmoke-950 font-semibold text-[40px] md:text-[40px] sm:h2-semibold sm:text-[36px] leading-[120%] tracking-[0%] ">
              Our Innovative Solutions for Your Delivery Services.
            </h1>
          </div>
          <div className="space-y-4 md:space-y-6">
            <p className="text-regular md:text-lg text-woodsmoke-800: #454545 sm:text-regular sm:font-medium sm:text-[18px] ">
              Koolboks Logistics offers a range of services designed to meet diverse needs. Whether for homes, businesses, our services ensure reliable cooling without reliance on traditional energy sources.
            </p>
            <Link href="/services" className=" btn-primary">
              Our Services
            </Link>
          </div>
        </div>

        {/* Full Width Globe Image */}
        <div className="w-full">
          <div className="w-full h-[200px] md:h-[400px] relative flex items-center justify-center">
            <Image
              src="/images/globe.jpg"
              alt="Global Network"
              width={1200}
              height={400}
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      </div>

      {/* Team Image Section - Mobile Only */}
      <section className="py-0 bg-white lg:hidden">
        <div className="mx-auto px-2">
            <Image
              src="/images/team.svg"
              alt="Our Team"
              width={350}
              height={312}
              className=" object-contain"
            />
          
        </div>
      </section>

      {/* Values Section */}
      <section className="pt-6 pb-8 md:py-10 lg:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:grid lg:grid-cols-2 gap-8 md:gap-16 items-center">
          <div className="relative order-2 lg:order-1 hidden lg:block">
            <div className="w-full h-[416px] rounded-2xl overflow-hidden">
              <Image
                src="/images/team.svg"
                alt="Our Team"
                width={576}
                height={4}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          <div className="h-auto lg:h-[416px] flex flex-col justify-between lg:order-2">
            <div className="space-y-4 md:space-y-4">
              <h2 className=" h3-semibold text-[40px] md:text-[32px] sm:text-[32px] leading-[120%] tracking-[0%] text-woodsmoke-950  mt-8 mb-6">
                The values that drive everything we do.
              </h2>
              <div className="flex gap-8 relative mb-6">
                <button
                  onClick={() => setActiveTab('mission')}
                  className={`pb-3 px-0 font-semibold transition-colors relative text-[18px] ${
                    activeTab === 'mission' ? 'text-primary-500' : 'text-woodsmoke-600 hover:text-woodsmoke-950'
                  }`}
                >
                  Our Mission
                  {activeTab === 'mission' && (
                    <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-primary-500 rounded-full"></div>
                  )}
                </button>
                <button
                  onClick={() => setActiveTab('values')}
                  className={`pb-3 px-0 text-medium font-semibold transition-colors relative text-[18px] ${
                    activeTab === 'values' ? 'text-primary-500' : 'text-woodsmoke-600 hover:text-woodsmoke-950'
                  }`}
                >
                  Our Values
                  {activeTab === 'values' && (
                    <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-primary-500 rounded-full"></div>
                  )}
                </button>
              </div>
            </div>

            {/* Mission Content */}
            {activeTab === 'mission' && (
              <div className="space-y-4 lg:flex-1 lg:flex lg:flex-col lg:justify-center">
                <h3 className="text-large font-semibold text-[20px] text-woodsmoke-950 ">
                  Redefining Logistics for a Faster, Smarter World
                </h3>
                <p className="text-[16px] text-regular text-Woodsmoke-800: #454545">
                 For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists
                </p>
              </div>
            )}

            {/* Values Content */}
            {activeTab === 'values' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:flex-1 lg:content-center">
                {/* Reliability */}
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center">
                    <Image
                      src="/icons/Headset.svg"
                      alt="Reliability Icon"
                      width={24}
                      height={24}
                      className="w-6 h-6 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-[16px] text-regular font-semibold text-woodsmoke-950 sm:text- leading-[130%]">Reliability</h3>
                  <p className="text-[14px] text-small text-woodsmoke-800 leading-[150%]">
                    Ensuring every shipment reaches its destination safely and on time.
                  </p>
                </div>

                {/* Innovation */}
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center">
                    <Image
                      src="/icons/currency.svg"
                      alt="Innovation Icon"
                      width={24}
                      height={24}
                      className="w-6 h-6 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-[16px] text-regular font-semibold text-woodsmoke-950 leading-[130%]">Innovation</h3>
                  <p className="text-[14px] text-small text-woodsmoke-800 leading-[150%]">
                    Leveraging cutting-edge technology for smarter logistics solutions.
                  </p>
                </div>

                {/* Customer Centric Approach */}
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center">
                    <Image
                      src="/icons/Clock.svg"
                      alt="Customer Centric Icon"
                      width={24}
                      height={24}
                      className="w-6 h-6 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-[16px] text-regular font-semibold text-woodsmoke-950 leading-[130%]">Customer Centric Approach</h3>
                  <p className="text-[14px] text-small text-woodsmoke-800 leading-[150%]">
                    Prioritizing client needs with tailored logistics strategies.
                  </p>
                </div>

                {/* Sustainability */}
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center">
                    <Image
                      src="/icons/Truck.svg"
                      alt="Sustainability Icon"
                      width={24}
                      height={24}
                      className="w-6 h-6 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-[16px] text-regular font-semibold text-woodsmoke-950 leading-[130%]">Sustainability</h3>
                  <p className="text-[14px] text-small text-woodsmoke-800 leading-[150%]">
                    Committing to eco-friendly practices for a greener supply chain.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-6 md:py-6 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="w-full max-w-[980px] h-auto md:h-[106px] mx-auto grid grid-cols-1 md:flex md:justify-between items-center opacity-100 gap-4 md:gap-0">
            <div className="flex flex-col items-center justify-center text-center ">
              <div className="  h1-semibold text-center text-primary-500">1M+</div>
              <p className="text-woodsmoke-800: #454545 text-medium  md:text-medium">Shipments Delivered</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="h1-semibold text-center text-primary-500">99%</div>
              <p className="text-woodsmoke-800: #454545 text-medium  md:text-medium">On-time delivery Rate</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="h1-semibold text-center text-primary-500">25</div>
              <p className="text-woodsmoke-800: #454545 text-medium  md:text-medium">Delivery Locations</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="h1-semibold text-center text-primary-500">8+</div>
              <p className="text-woodsmoke-800: #454545 text-medium  md:text-medium">Delivery Partners</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="pt-8 pb-0 md:py-16 lg:py-20 px-4 md:px-6">
        <div className="max-w-[1200px] h-[421.2] ">
          <LandingPageContact />
        </div>
      </section>


    </div>
  )
}

export default Company
