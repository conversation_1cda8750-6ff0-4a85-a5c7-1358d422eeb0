'use client'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { getAllBlogPosts } from '@/data/blogPosts'

const Blog = () => {
  const blogPosts = getAllBlogPosts()

  return (
    <div className="min-h-screen bg-white pt-14">
      {/* Header Section */}
      <section className="py-8 md:py- bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid lg:grid-cols-2 gap-6 md:gap-12 items-start mb-8 md:mb-12 lg:">
            <div className="space-y-4 md:space-y-6">
              <div className="flex items-center gap-2">
                <Image
                  src="/icons/cubeicon.svg"
                  alt="Cube Icon"
                  width={20}
                  height={20}
                  className="w-4 h-4 md:w-5 md:h-5"
                />
                <p className="text-small text-woodsmoke-800 font-semibold  md:text-small tracking-wider ">BLOG</p>
              </div>
              <h1 className="h3-semibold sm:h2-semibold sm:text-[32px] text-[40px] md:text-[40px] leading-[120%] tracking-[0%] text-woodsmoke-950">
                Insights into the logistics industry
              </h1>
            </div>
            <div className="space-y-4 md:space-y-6">
              <p className="text-regular text-[18px] md:text-medium text-woodsmoke-800 lg:py-16 leading-relaxed">
                Discover how KOOL LOGISTICS has transformed logistics challenges into success stories for businesses across diverse sectors
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pt-0 pb-6 md:pt-0 md:pb-8 lg:pt-0 lg:pb-8 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          {/* Mobile Layout */}
          <div className="grid grid-cols-1 gap-8 justify-items-center md:hidden">
            {blogPosts.map((post) => (
              <Link key={post.id} href={`/blog/${post.slug}`} className="block">
                <article className="w-full max-w-[343px] h-[400px] bg-white rounded-[12px] border-[0.92px] border-gray-200 p-3 flex flex-col gap-2 opacity-100 rotate-0 hover:shadow-md transition-shadow duration-300 cursor-pointer">
                  <div className="w-full h-[200px] relative overflow-hidden rounded-[8px]">
                    <Image
                      src={post.heroImage.src}
                      alt={post.heroImage.alt}
                      width={343}
                      height={200}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex flex-col gap-2 flex-1">
                    <h3 className=" font-semibold text-[20px] text-[#171717] text-large leading-[140%] tracking-[0%]">
                      {post.title}
                    </h3>
                    <p className="text-regular font-normal text-[16px] text-[#171717] leading-[150%] tracking-[0%] line-clamp-3">
                      {post.excerpt}
                    </p>
                  </div>
                </article>
              </Link>
            ))}
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:block">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[24px] justify-items-center">
              {blogPosts.map((post) => (
                <Link key={post.id} href={`/blog/${post.slug}`} className="block">
                  <article className="w-[384px] h-[520px] rounded-[20px] border-[0.92px] p-[16px] flex flex-col gap-[12px] opacity-100 rotate-0 hover:shadow-md transition-shadow duration-300 cursor-pointer">
                    <div className="w-full h-[280px] relative overflow-hidden rounded-[16px]">
                      <Image
                        src={post.heroImage.src}
                        alt={post.heroImage.alt}
                        width={352}
                        height={300}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex flex-col gap-[8px] flex-1">
                      <h3 className="font-semibold text-[20px] text-large text-woodsmoke-950 leading-[130%] tracking-[0%]">
                        {post.title}
                      </h3>
                      <p className="text-regular text-[16px] text-woodsmoke-950 leading-[150%] tracking-[0%] line-clamp-3">
                        {post.excerpt}
                      </p>
                    </div>
                  </article>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Blog